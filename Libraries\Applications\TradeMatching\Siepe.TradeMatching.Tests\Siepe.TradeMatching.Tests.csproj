<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>

    <IsPackable>false</IsPackable>
    <IsTestProject>true</IsTestProject>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="coverlet.collector" Version="6.0.0" />
    <PackageReference Include="Microsoft.NET.Test.Sdk" Version="17.8.0" />
    <PackageReference Include="NSubstitute" Version="5.3.0" />
    <PackageReference Include="xunit" Version="2.5.3" />
    <PackageReference Include="xunit.runner.visualstudio" Version="2.5.3" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\Siepe.TradeMatching.Entities\Siepe.TradeMatching.Entities.csproj" />
    <ProjectReference Include="..\Siepe.TradeMatching.Services.CTM\Siepe.TradeMatching.Services.CTM.csproj" />
    <ProjectReference Include="..\Siepe.TradeMatching.DataAccess\Siepe.TradeMatching.DataAccess.csproj" />
    <ProjectReference Include="..\..\Infrastructure\PubSub\Siepe.Infrastructure.PubSub.Common\Siepe.Infrastructure.PubSub.Common.csproj" />
    <ProjectReference Include="..\..\DBUtility\DBUtility.Common\DBUtility.Common.csproj" />
  </ItemGroup>

  <ItemGroup>
    <Using Include="Xunit" />
  </ItemGroup>

</Project>
