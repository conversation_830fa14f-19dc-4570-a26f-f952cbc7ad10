using System;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;
using NSubstitute;
using Siepe.Infrastructure.PubSub.Common;
using Siepe.TradeMatching.Services.CTM;
using Siepe.TradeMatching.Services.CTM.MessageHandlers;
using Siepe.TradeMatching.Entities;
using Xunit;

namespace Siepe.TradeMatching.Tests.Handlers
{
    public class SubmitSingleTradeHandlerTests
    {
        private readonly ILogger<SubmitSingleTradeHandler> _logger;
        private readonly ICtmService _ctmService;
        private readonly SubmitSingleTradeHandler _handler;

        public SubmitSingleTradeHandlerTests()
        {
            _logger = Substitute.For<ILogger<SubmitSingleTradeHandler>>();
            _ctmService = Substitute.For<ICtmService>();
            _handler = new SubmitSingleTradeHandler(_logger, _ctmService);
        }

        [Theory]
        [InlineData(".CTMAdapter.SubmitSingle", true)]
        [InlineData(".CTMAdapter.SubmitSingle.Trade123", true)]
        [InlineData("Prefix.CTMAdapter.SubmitSingle", true)]
        [InlineData(".CTMAdapter.SubmitBatch", false)]
        [InlineData(".CTMAdapter.Different", false)]
        [InlineData("InvalidTopic", false)]
        public void IsApplicable_ShouldReturnCorrectResult_ForVariousTopics(string topic, bool expectedResult)
        {
            // Arrange
            var message = new PublishedMessageMetadata { Topic = topic };
            var payload = "test payload";

            // Act
            var result = _handler.IsApplicable(message, payload);

            // Assert
            Assert.Equal(expectedResult, result);
        }

        [Fact]
        public async Task ProcessAsync_ShouldProcessValidMessage_Successfully()
        {
            // Arrange
            var allocationIdentifier = new AllocationIdentifier
            {
                BlockReferenceNumber = "BLK123",
                AllocationReferenceNumber = "ALLOC456",
                CTMTradeSideId = "TS789",
                CTMTradeDetailId = "TD012"
            };

            var xmlPayload = SerializeToXml(allocationIdentifier);
            var message = new PublishedMessageMetadata
            {
                Topic = ".CTMAdapter.SubmitSingle"
            };

            // Act
            await _handler.ProcessAsync(message, xmlPayload);

            // Assert
            await _ctmService.Received(1).ProcessTrade(
                Arg.Is<AllocationIdentifier>(x =>
                    x.BlockReferenceNumber == allocationIdentifier.BlockReferenceNumber &&
                    x.AllocationReferenceNumber == allocationIdentifier.AllocationReferenceNumber &&
                    x.CTMTradeSideId == allocationIdentifier.CTMTradeSideId &&
                    x.CTMTradeDetailId == allocationIdentifier.CTMTradeDetailId
                )
            );
        }

        [Fact]
        public async Task ProcessAsync_WhenCtmServiceThrowsException_ShouldLogErrorAndRethrow()
        {
            // Arrange
            var allocationIdentifier = new AllocationIdentifier
            {
                BlockReferenceNumber = "BLK123",
                AllocationReferenceNumber = "ALLOC456"
            };

            var xmlPayload = SerializeToXml(allocationIdentifier);
            var message = new PublishedMessageMetadata
            {
                Topic = ".CTMAdapter.SubmitSingle"
            };

            var expectedException = new Exception("CTM Service error");
            _ctmService.When(x => x.ProcessTrade(Arg.Any<AllocationIdentifier>()))
                .Do(x => throw expectedException);

            // Act & Assert
            var actualException = await Assert.ThrowsAsync<Exception>(() => _handler.ProcessAsync(message, xmlPayload));
            
            Assert.Equal(expectedException.Message, actualException.Message);
            
            // Verify error was logged
            _logger.Received(1).LogError(
                expectedException,
                Arg.Is<string>(s => s.Contains("Error processing SubmitSingleTradeHandler"))
            );
        }

        [Fact]
        public async Task ProcessAsync_WithInvalidXml_ShouldThrowException()
        {
            // Arrange
            var message = new PublishedMessageMetadata
            {
                Topic = ".CTMAdapter.SubmitSingle"
            };
            var invalidPayload = "Invalid XML Content";

            // Act & Assert
            await Assert.ThrowsAsync<Exception>(() => _handler.ProcessAsync(message, invalidPayload));
        }

        [Fact]
        public async Task ProcessAsync_WithNullPayload_ShouldThrowException()
        {
            // Arrange
            var message = new PublishedMessageMetadata
            {
                Topic = ".CTMAdapter.SubmitSingle"
            };

            // Act & Assert
            await Assert.ThrowsAsync<Exception>(() => _handler.ProcessAsync(message, null));
        }

        [Fact]
        public async Task ProcessAsync_ShouldCallProcessAllocationAndProcessResponseMessage()
        {
            // Arrange
            var allocationIdentifier = new AllocationIdentifier
            {
                BlockReferenceNumber = "BLK123",
                AllocationReferenceNumber = "ALLOC456",
                CTMTradeSideId = "TS789",
                CTMTradeDetailId = "TD012"
            };

            var xmlPayload = SerializeToXml(allocationIdentifier);
            var message = new PublishedMessageMetadata
            {
                Topic = ".CTMAdapter.SubmitSingle"
            };

            // Setup ProcessTrade to simulate calling ProcessResponseMessages
            _ctmService.ProcessTrade(Arg.Any<AllocationIdentifier>())
                .Returns(Task.CompletedTask)
                .AndDoes(async callInfo =>
                {
                    // Simulate the internal flow of ProcessTrade
                    await _ctmService.ProcessResponseMessages();
                });

            // Act
            await _handler.ProcessAsync(message, xmlPayload);

            // Assert
            await _ctmService.Received(1).ProcessTrade(Arg.Any<AllocationIdentifier>());
            await _ctmService.Received(1).ProcessResponseMessages();
        }

        [Fact]
        public async Task ProcessAsync_ShouldLogIncomingMessage()
        {
            // Arrange
            var allocationIdentifier = new AllocationIdentifier
            {
                BlockReferenceNumber = "BLK123",
                AllocationReferenceNumber = "ALLOC456"
            };

            var xmlPayload = SerializeToXml(allocationIdentifier);
            var message = new PublishedMessageMetadata
            {
                Topic = ".CTMAdapter.SubmitSingle"
            };

            // Act
            await _handler.ProcessAsync(message, xmlPayload);

            // Assert
            _logger.Received().LogInformation(
                Arg.Is<string>(s => s.Contains("Received message"))
            );
        }

        private string SerializeToXml(AllocationIdentifier allocationIdentifier)
        {
            return $@"<?xml version=""1.0"" encoding=""utf-8""?>
<AllocationIdentifier>
    <BlockReferenceNumber>{allocationIdentifier.BlockReferenceNumber}</BlockReferenceNumber>
    <AllocationReferenceNumber>{allocationIdentifier.AllocationReferenceNumber}</AllocationReferenceNumber>
    <CTMTradeSideId>{allocationIdentifier.CTMTradeSideId}</CTMTradeSideId>
    <CTMTradeDetailId>{allocationIdentifier.CTMTradeDetailId}</CTMTradeDetailId>
</AllocationIdentifier>";
        }
    }
}